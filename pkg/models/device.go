package models

// DeviceType represents the type of device in GB/T 28181
type DeviceType string

const (
	DeviceTypeAdministrativeRegion DeviceType = "administrative_region" // 行政区划 (2-8位)
	DeviceTypeCamera               DeviceType = "camera"                // 摄像头 (20位,类型131/132)
	DeviceTypeBusinessGroup        DeviceType = "business_group"        // 业务分组 (20位,类型215)
	DeviceTypeVirtualOrganization  DeviceType = "virtual_organization"  // 虚拟组织 (20位,类型216)
	DeviceTypeSystemDirectory      DeviceType = "system_directory"      // 系统目录 (20位,类型200)
	DeviceTypeUnknown              DeviceType = "unknown"               // 未知类型
)

// Device represents a device or directory node in GB/T 28181
type Device struct {
	GBID       string `json:"gb_id" xml:"DeviceID"`
	Name       string `json:"name" xml:"Name"`
	Status     string `json:"status" xml:"Status"`
	IP         string `json:"ip,omitempty" xml:"IPAddress,omitempty"`
	PlatformID string `json:"platform_id"`

	// 新增字段用于支持递归目录查询
	DeviceType      DeviceType `json:"device_type"`                                                 // 设备类型
	ParentID        string     `json:"parent_id,omitempty" xml:"ParentID,omitempty"`                // 父节点ID
	BusinessGroupID string     `json:"business_group_id,omitempty" xml:"BusinessGroupID,omitempty"` // 业务分组ID
	CivilCode       string     `json:"civil_code,omitempty" xml:"CivilCode,omitempty"`              // 行政区划代码
	Block           string     `json:"block,omitempty" xml:"Block,omitempty"`                       // 组织机构代码
	Address         string     `json:"address,omitempty" xml:"Address,omitempty"`                   // 设备安装地址
	Manufacturer    string     `json:"manufacturer,omitempty" xml:"Manufacturer,omitempty"`         // 设备厂商
	Model           string     `json:"model,omitempty" xml:"Model,omitempty"`                       // 设备型号
	Owner           string     `json:"owner,omitempty" xml:"Owner,omitempty"`                       // 设备归属
	Parental        int        `json:"parental,omitempty" xml:"Parental,omitempty"`                 // 是否有子设备
	RegisterWay     string     `json:"register_way,omitempty" xml:"RegisterWay,omitempty"`          // 注册方式
	Secrecy         int        `json:"secrecy,omitempty" xml:"Secrecy,omitempty"`                   // 保密属性

	IsRealDevice bool `json:"is_real_device"` // 是否为真实设备（摄像头）
}

// DeviceList represents the response from catalog query
type DeviceList struct {
	Devices []Device `json:"devices"`
	Total   int      `json:"total"`
}

// GetDeviceType 根据设备ID判断设备类型
func GetDeviceType(deviceID string) DeviceType {
	length := len(deviceID)

	// 行政区划：2、4、6、8位数字
	if length == 2 || length == 4 || length == 6 || length == 8 {
		return DeviceTypeAdministrativeRegion
	}

	// 20位设备编码
	if length == 20 {
		// 提取类型编码（第11-13位）
		if len(deviceID) >= 13 {
			typeCode := deviceID[10:13]
			switch typeCode {
			case "131", "132": // 摄像机、网络摄像机
				return DeviceTypeCamera
			case "215": // 业务分组
				return DeviceTypeBusinessGroup
			case "216": // 虚拟组织
				return DeviceTypeVirtualOrganization
			case "200": // 系统目录
				return DeviceTypeSystemDirectory
			}
		}
	}

	return DeviceTypeUnknown
}

// IsRealCamera 判断是否为真实的摄像头设备
func (d *Device) IsRealCamera() bool {
	return d.DeviceType == DeviceTypeCamera
}

// UpdateDeviceType 更新设备类型和相关属性
func (d *Device) UpdateDeviceType() {
	d.DeviceType = GetDeviceType(d.GBID)
	d.IsRealDevice = d.IsRealCamera()
}
