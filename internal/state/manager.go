package state

import (
	"fmt"
	"log/slog"
	"sync"
	"time"

	"gb-gateway/pkg/models"
)

// Manager manages application state
type Manager struct {
	platforms sync.Map // map[string]*models.Platform
	devices   sync.Map // map[string]*models.Device
	sessions  sync.Map // map[string]*models.StreamSession

	// Channels for async operations
	catalogResponses sync.Map // map[string]chan []models.Device
}

// NewManager creates a new state manager
func NewManager() *Manager {
	m := &Manager{}
	return m
}

// Platform operations
func (m *Manager) RegisterPlatform(platform *models.Platform) {
	platform.LastSeen = time.Now()
	m.platforms.Store(platform.ID, platform)
	slog.Info("Platform registered", "id", platform.ID, "uri", platform.SIPURI)
}

func (m *Manager) GetPlatform(id string) (*models.Platform, bool) {
	if value, ok := m.platforms.Load(id); ok {
		return value.(*models.Platform), true
	}
	return nil, false
}

func (m *Manager) UpdatePlatformLastSeen(id string) {
	if value, ok := m.platforms.Load(id); ok {
		platform := value.(*models.Platform)
		platform.LastSeen = time.Now()
		m.platforms.Store(id, platform)
	}
}

func (m *Manager) GetAllPlatforms() []*models.Platform {
	var platforms []*models.Platform
	m.platforms.Range(func(key, value any) bool {
		platforms = append(platforms, value.(*models.Platform))
		return true
	})
	return platforms
}

// Device operations
func (m *Manager) UpdateDevices(platformID string, devices []models.Device) {
	for _, device := range devices {
		if device.GBID == "" {
			slog.Error("Invalid device", "device", device)
			continue
		}
		device.PlatformID = platformID
		// 更新设备类型和相关属性
		device.UpdateDeviceType()
		m.devices.Store(device.GBID, &device)
	}

	slog.Info("Devices updated", "platform_id", platformID, "count", len(devices))
}

func (m *Manager) GetDevice(gbID string) (*models.Device, bool) {
	if value, ok := m.devices.Load(gbID); ok {
		return value.(*models.Device), true
	}
	return nil, false
}

func (m *Manager) GetAllDevices() []models.Device {
	var devices []models.Device
	m.devices.Range(func(key, value any) bool {
		devices = append(devices, *value.(*models.Device))
		return true
	})
	return devices
}

// GetRealCameraDevices 获取真实的摄像头设备（过滤掉行政区划等）
func (m *Manager) GetRealCameraDevices(platformID string) []models.Device {
	var cameras []models.Device
	m.devices.Range(func(key, value any) bool {
		device := value.(*models.Device)
		// 只返回真实的摄像头设备
		if device.IsRealCamera() && (platformID == "" || device.PlatformID == platformID) {
			cameras = append(cameras, *device)
		}
		return true
	})
	return cameras
}

// Session operations
func (m *Manager) CreateSession(session *models.StreamSession) {
	session.StartTime = time.Now()
	session.Status = "requesting"
	m.sessions.Store(session.SessionID, session)
	slog.Info("Session created", "session_id", session.SessionID, "gb_id", session.GBID)
}

func (m *Manager) GetSession(sessionID string) (*models.StreamSession, bool) {
	if value, ok := m.sessions.Load(sessionID); ok {
		return value.(*models.StreamSession), true
	}
	return nil, false
}

func (m *Manager) UpdateSessionStatus(sessionID, status string) {
	if value, ok := m.sessions.Load(sessionID); ok {
		session := value.(*models.StreamSession)
		session.Status = status
		m.sessions.Store(sessionID, session)
		slog.Info("Session status updated", "session_id", sessionID, "status", status)
	}
}

func (m *Manager) DeleteSession(sessionID string) {
	m.sessions.Delete(sessionID)
	slog.Info("Session deleted", "session_id", sessionID)
}

// Async catalog operations
func (m *Manager) CreateCatalogChannel(sn string) chan []models.Device {
	ch := make(chan []models.Device, 1)
	m.catalogResponses.Store(sn, ch)

	// Auto cleanup after timeout
	go func() {
		time.Sleep(30 * time.Second)
		m.catalogResponses.Delete(sn)
		close(ch)
	}()

	return ch
}

func (m *Manager) SendCatalogResponse(sn string, devices []models.Device) error {
	if value, ok := m.catalogResponses.Load(sn); ok {
		ch := value.(chan []models.Device)
		select {
		case ch <- devices:
			m.catalogResponses.Delete(sn)
			return nil
		default:
			return fmt.Errorf("catalog channel is full or closed")
		}
	}
	return fmt.Errorf("catalog request with SN %s not found", sn)
}
