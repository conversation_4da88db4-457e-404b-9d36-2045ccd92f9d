# GB-Gateway Project Overview

This project is a Go-based gateway service for the GB/T 28181 protocol, designed to connect a business platform with a national standard video platform (e.g., Hikvision security platform). It acts as a translator, converting simple HTTP RESTful API calls into the more complex GB/T 28181 SIP signaling required by video surveillance systems.

The gateway is designed to be lightweight and high-performance by focusing solely on signaling and not handling the video stream (RTP) itself.

## Key Technologies

*   **Language:** Go
*   **Web Framework:** Gin
*   **SIP Stack:** `panjjo/gosip`
*   **Configuration:** Viper (using `config.yaml`)
*   **Logging:** `slog` (standard library)
*   **API Documentation:** Swagger (Swag)

## Architecture

The application is composed of several key modules:

*   **HTTP Server (`internal/http`):** Exposes a RESTful API for upstream services to interact with the gateway. It handles requests for device lists, video stream initiation, and PTZ control.
*   **SIP Server (`internal/sip`):** Manages all GB/T 28181 communication with the downstream video platform. It handles device registration, catalog queries, and sending INVITE/MESSAGE requests for streaming and control.
*   **Core Logic (`internal/core`):** Acts as the bridge between the HTTP and SIP servers, containing the main business logic for protocol translation and workflow control.
*   **State Manager (`internal/state`):** An in-memory store for managing the state of registered platforms, devices, and active stream sessions.

```mermaid
graph TD
    UpstreamService[Business Platform] -- HTTP API --> Gateway_HTTP[HTTP Server]
    Gateway_HTTP -- Calls --> Gateway_Core[Core Logic]
    Gateway_Core -- Manages --> Gateway_State[In-Memory State]
    Gateway_Core -- Instructs --> Gateway_SIP[SIP Server]
    Gateway_SIP -- SIP/GB28181 --> VideoPlatform[Video Platform]
```

## Building and Running

The project uses a `Makefile` to simplify common tasks.

### Prerequisites

*   Go (version 1.25 or higher)
*   Git

### Quick Start

1.  **Clone the repository:**
    ```bash
    git clone <repository-url>
    cd gb-gateway
    ```

2.  **Create a configuration file:**
    Copy the example configuration and modify it as needed.
    ```bash
    cp config.example.yaml config.yaml
    ```

3.  **Build and Run:**
    The `run` command will automatically build the application first.
    ```bash
    make run
    ```

4.  **Run with Hot-Reload (Development):**
    For development, you can use `air` for automatic reloading on file changes.
    ```bash
    make dev
    ```

### Key `make` Commands

*   `make build`: Compiles the Go binary into the `./build` directory.
*   `make run`: Builds and runs the application.
*   `make test`: Runs all tests.
*   `make clean`: Removes build artifacts.
*   `make deps`: Downloads Go module dependencies.
*   `make lint`: Runs the `golangci-lint` linter.
*   `make format`: Formats the Go code.
*   `make swagger-gen`: Regenerates the Swagger API documentation.

## Configuration

The application is configured via `config.yaml`.

```yaml
server:
  http_port: 8080       # HTTP service port
  sip_ip: "0.0.0.0"     # SIP listening IP
  sip_port: 5060        # SIP listening port
  sip_id: "34020000002000000001" # Gateway's national standard ID
  sip_domain: "3402000000"       # Gateway's domain
  device_status_interval: 30    # Device status query interval (seconds)

log:
  level: "debug"        # Log level: debug, info, warn, error
  path: "/var/log/gb-gateway.log"
```

**Note:** Setting `log.level` to `"debug"` enables the Swagger UI.

## API Documentation

When the server is running in `debug` mode, a Swagger UI is available for exploring and testing the API.

*   **Swagger UI:** `http://localhost:8080/swagger/index.html`

To regenerate the documentation after making API changes, run:
```bash
make swagger-gen
```

## Development Conventions

*   **Structure:** The project follows the standard Go project layout, separating the main application entry point (`cmd`), internal logic (`internal`), and reusable packages (`pkg`).
*   **Testing:** Tests are located in `_test.go` files alongside the code they are testing (e.g., `internal/sip/server_test.go`). Tests can be run with `make test`.
*   **Linting & Formatting:** Code quality is maintained using `golangci-lint` and `go fmt`. Use `make lint` and `make format` to check and format the code.
